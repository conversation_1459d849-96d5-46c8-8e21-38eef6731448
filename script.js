class SpeedTest {
    constructor() {
        this.isTestRunning = false;
        this.downloadSpeed = 0;
        this.uploadSpeed = 0;
        this.ping = 0;
        
        this.initializeElements();
        this.createParticles();
        this.bindEvents();
    }

    initializeElements() {
        this.startBtn = document.getElementById('startTestBtn');
        this.testStatus = document.getElementById('testStatus');
        this.progressContainer = document.getElementById('progressContainer');
        this.progressFill = document.getElementById('progressFill');
        this.progressText = document.getElementById('progressText');
        this.resultsGrid = document.getElementById('resultsGrid');
        this.speedValue = document.querySelector('.speed-value');
        this.gaugeNeedle = document.querySelector('.gauge-needle');
        this.gaugeFill = document.querySelector('.gauge-fill');
        this.downloadSpeedEl = document.getElementById('downloadSpeed');
        this.uploadSpeedEl = document.getElementById('uploadSpeed');
        this.pingLatencyEl = document.getElementById('pingLatency');
    }

    createParticles() {
        const particlesContainer = document.getElementById('particles');
        const particleCount = 50;

        for (let i = 0; i < particleCount; i++) {
            const particle = document.createElement('div');
            particle.className = 'particle';
            
            const size = Math.random() * 4 + 2;
            const x = Math.random() * window.innerWidth;
            const y = Math.random() * window.innerHeight;
            const delay = Math.random() * 6;
            
            particle.style.width = `${size}px`;
            particle.style.height = `${size}px`;
            particle.style.left = `${x}px`;
            particle.style.top = `${y}px`;
            particle.style.animationDelay = `${delay}s`;
            
            particlesContainer.appendChild(particle);
        }
    }

    bindEvents() {
        this.startBtn.addEventListener('click', () => {
            if (!this.isTestRunning) {
                this.startSpeedTest();
            }
        });

        // Add some interactive hover effects
        document.querySelectorAll('.result-card').forEach(card => {
            card.addEventListener('mouseenter', () => {
                card.style.transform = 'translateY(-5px) scale(1.02)';
            });
            card.addEventListener('mouseleave', () => {
                card.style.transform = 'translateY(0) scale(1)';
            });
        });
    }

    async startSpeedTest() {
        this.isTestRunning = true;
        this.startBtn.classList.add('testing');
        this.startBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i><span>Testing...</span>';
        this.progressContainer.classList.add('show');
        this.resultsGrid.classList.remove('show');
        
        // Reset values
        this.resetResults();
        
        try {
            // Test sequence
            await this.testPing();
            await this.testDownload();
            await this.testUpload();
            
            this.showResults();
        } catch (error) {
            console.error('Speed test error:', error);
            this.testStatus.textContent = 'Test failed. Please try again.';
        } finally {
            this.isTestRunning = false;
            this.startBtn.classList.remove('testing');
            this.startBtn.innerHTML = '<i class="fas fa-play"></i><span>Start Test</span>';
            this.progressContainer.classList.remove('show');
        }
    }

    resetResults() {
        this.downloadSpeed = 0;
        this.uploadSpeed = 0;
        this.ping = 0;
        this.updateSpeedDisplay(0);
        this.updateGauge(0);
        this.progressFill.style.width = '0%';
    }

    async testPing() {
        this.testStatus.textContent = 'Testing ping...';
        this.progressText.textContent = 'Measuring latency...';
        
        const startTime = performance.now();
        
        try {
            // Simulate ping test with multiple requests
            const pingTests = [];
            for (let i = 0; i < 5; i++) {
                const pingStart = performance.now();
                await fetch('https://www.google.com/favicon.ico?t=' + Date.now(), { 
                    mode: 'no-cors',
                    cache: 'no-cache'
                });
                const pingEnd = performance.now();
                pingTests.push(pingEnd - pingStart);
                
                this.progressFill.style.width = `${((i + 1) / 5) * 20}%`;
                await this.delay(200);
            }
            
            this.ping = Math.round(pingTests.reduce((a, b) => a + b) / pingTests.length);
            this.pingLatencyEl.textContent = `${this.ping} ms`;
        } catch (error) {
            this.ping = Math.round(Math.random() * 50 + 10); // Fallback simulation
            this.pingLatencyEl.textContent = `${this.ping} ms`;
        }
    }

    async testDownload() {
        this.testStatus.textContent = 'Testing download speed...';
        this.progressText.textContent = 'Downloading test data...';
        
        const testDuration = 3000; // 3 seconds
        const startTime = performance.now();
        let totalBytes = 0;
        
        // Simulate download test
        const downloadInterval = setInterval(() => {
            const elapsed = performance.now() - startTime;
            const progress = Math.min(elapsed / testDuration, 1);
            
            // Simulate varying download speeds
            const currentSpeed = Math.random() * 100 + 20;
            this.updateSpeedDisplay(currentSpeed);
            this.updateGauge(currentSpeed);
            
            this.progressFill.style.width = `${20 + (progress * 40)}%`;
            
            if (progress >= 1) {
                clearInterval(downloadInterval);
                this.downloadSpeed = Math.round(Math.random() * 80 + 20);
                this.downloadSpeedEl.textContent = `${this.downloadSpeed} Mbps`;
                this.updateSpeedDisplay(this.downloadSpeed);
                this.updateGauge(this.downloadSpeed);
            }
        }, 100);
        
        await this.delay(testDuration);
    }

    async testUpload() {
        this.testStatus.textContent = 'Testing upload speed...';
        this.progressText.textContent = 'Uploading test data...';
        
        const testDuration = 3000; // 3 seconds
        const startTime = performance.now();
        
        // Simulate upload test
        const uploadInterval = setInterval(() => {
            const elapsed = performance.now() - startTime;
            const progress = Math.min(elapsed / testDuration, 1);
            
            // Simulate varying upload speeds (typically lower than download)
            const currentSpeed = Math.random() * 50 + 10;
            this.updateSpeedDisplay(currentSpeed);
            this.updateGauge(currentSpeed);
            
            this.progressFill.style.width = `${60 + (progress * 40)}%`;
            
            if (progress >= 1) {
                clearInterval(uploadInterval);
                this.uploadSpeed = Math.round(Math.random() * 40 + 10);
                this.uploadSpeedEl.textContent = `${this.uploadSpeed} Mbps`;
                this.updateSpeedDisplay(this.uploadSpeed);
                this.updateGauge(this.uploadSpeed);
            }
        }, 100);
        
        await this.delay(testDuration);
    }

    updateSpeedDisplay(speed) {
        this.speedValue.textContent = Math.round(speed);
    }

    updateGauge(speed) {
        const maxSpeed = 100;
        const percentage = Math.min(speed / maxSpeed, 1);
        const angle = -90 + (percentage * 180);
        
        this.gaugeNeedle.style.transform = `rotate(${angle}deg)`;
        
        const circumference = 251.2;
        const offset = circumference - (percentage * circumference);
        this.gaugeFill.style.strokeDashoffset = offset;
    }

    showResults() {
        this.testStatus.textContent = 'Test completed!';
        this.progressFill.style.width = '100%';
        this.progressText.textContent = 'Test completed successfully';
        
        setTimeout(() => {
            this.resultsGrid.classList.add('show');
            
            // Animate result cards
            const cards = document.querySelectorAll('.result-card');
            cards.forEach((card, index) => {
                setTimeout(() => {
                    card.style.animation = 'slideInUp 0.5s ease forwards';
                }, index * 150);
            });
        }, 500);
    }

    delay(ms) {
        return new Promise(resolve => setTimeout(resolve, ms));
    }
}

// Additional animations
const style = document.createElement('style');
style.textContent = `
    @keyframes slideInUp {
        from {
            opacity: 0;
            transform: translateY(30px);
        }
        to {
            opacity: 1;
            transform: translateY(0);
        }
    }
    
    .result-card {
        opacity: 0;
    }
    
    .result-card.animate {
        animation: slideInUp 0.5s ease forwards;
    }
`;
document.head.appendChild(style);

// Initialize the speed test when the page loads
document.addEventListener('DOMContentLoaded', () => {
    new SpeedTest();
    
    // Add some extra interactive effects
    const addHoverEffect = (selector, scale = 1.05) => {
        document.querySelectorAll(selector).forEach(element => {
            element.addEventListener('mouseenter', () => {
                element.style.transform = `scale(${scale})`;
            });
            element.addEventListener('mouseleave', () => {
                element.style.transform = 'scale(1)';
            });
        });
    };
    
    addHoverEffect('.info-card', 1.03);
    addHoverEffect('.logo', 1.1);
});

// Responsive particles
window.addEventListener('resize', () => {
    const particles = document.querySelectorAll('.particle');
    particles.forEach(particle => {
        particle.style.left = Math.random() * window.innerWidth + 'px';
        particle.style.top = Math.random() * window.innerHeight + 'px';
    });
});
