/* Reset and Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Inter', sans-serif;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    min-height: 100vh;
    color: #333;
    overflow-x: hidden;
    position: relative;
}

/* Particles Background */
.particles {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    pointer-events: none;
    z-index: 1;
}

.particle {
    position: absolute;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 50%;
    animation: float 6s ease-in-out infinite;
}

@keyframes float {
    0%, 100% { transform: translateY(0px) rotate(0deg); opacity: 0.7; }
    50% { transform: translateY(-20px) rotate(180deg); opacity: 1; }
}

/* Container */
.container {
    position: relative;
    z-index: 2;
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
}

/* Header */
.header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 20px 0;
    backdrop-filter: blur(10px);
    background: rgba(255, 255, 255, 0.1);
    border-radius: 15px;
    margin: 20px 0;
    padding: 15px 30px;
    border: 1px solid rgba(255, 255, 255, 0.2);
}

.logo {
    display: flex;
    align-items: center;
    gap: 10px;
    font-size: 24px;
    font-weight: 700;
    color: white;
}

.logo i {
    font-size: 28px;
    color: #ffd93d;
}

.nav {
    display: flex;
    gap: 30px;
}

.nav-link {
    color: white;
    text-decoration: none;
    font-weight: 500;
    transition: all 0.3s ease;
    position: relative;
}

.nav-link:hover {
    color: #ffd93d;
    transform: translateY(-2px);
}

.nav-link::after {
    content: '';
    position: absolute;
    bottom: -5px;
    left: 0;
    width: 0;
    height: 2px;
    background: #ffd93d;
    transition: width 0.3s ease;
}

.nav-link:hover::after {
    width: 100%;
}

/* Hero Section */
.hero {
    text-align: center;
    padding: 60px 0;
    color: white;
}

.hero-title {
    font-size: 3.5rem;
    font-weight: 700;
    margin-bottom: 20px;
    background: linear-gradient(45deg, #fff, #ffd93d);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    animation: titleGlow 2s ease-in-out infinite alternate;
}

@keyframes titleGlow {
    from { filter: drop-shadow(0 0 10px rgba(255, 217, 61, 0.3)); }
    to { filter: drop-shadow(0 0 20px rgba(255, 217, 61, 0.6)); }
}

.hero-subtitle {
    font-size: 1.2rem;
    opacity: 0.9;
    max-width: 600px;
    margin: 0 auto;
    line-height: 1.6;
}

/* Speed Test Widget */
.speed-test-widget {
    margin: 40px 0;
}

.widget-container {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(20px);
    border-radius: 25px;
    padding: 40px;
    box-shadow: 0 20px 60px rgba(0, 0, 0, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.3);
    transition: all 0.3s ease;
}

.widget-container:hover {
    transform: translateY(-5px);
    box-shadow: 0 30px 80px rgba(0, 0, 0, 0.15);
}

/* Speed Gauge */
.speed-gauge {
    text-align: center;
    margin-bottom: 40px;
}

.gauge-container {
    position: relative;
    display: inline-block;
}

.gauge-svg {
    width: 300px;
    height: 180px;
}

.gauge-fill {
    transition: stroke-dashoffset 0.5s ease;
}

.gauge-needle {
    transition: transform 0.5s ease;
    transform-origin: 100px 100px;
}

.speed-display {
    position: absolute;
    bottom: 20px;
    left: 50%;
    transform: translateX(-50%);
    text-align: center;
}

.speed-value {
    font-size: 3rem;
    font-weight: 700;
    color: #333;
    display: block;
    line-height: 1;
}

.speed-unit {
    font-size: 1rem;
    color: #666;
    font-weight: 500;
}

/* Test Controls */
.test-controls {
    text-align: center;
    margin-bottom: 40px;
}

.start-test-btn {
    background: linear-gradient(45deg, #667eea, #764ba2);
    color: white;
    border: none;
    padding: 15px 40px;
    border-radius: 50px;
    font-size: 1.1rem;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    display: inline-flex;
    align-items: center;
    gap: 10px;
    box-shadow: 0 10px 30px rgba(102, 126, 234, 0.3);
}

.start-test-btn:hover {
    transform: translateY(-3px);
    box-shadow: 0 15px 40px rgba(102, 126, 234, 0.4);
}

.start-test-btn:active {
    transform: translateY(-1px);
}

.start-test-btn.testing {
    background: linear-gradient(45deg, #ff6b6b, #ee5a24);
    animation: pulse 1.5s ease-in-out infinite;
}

@keyframes pulse {
    0%, 100% { transform: scale(1); }
    50% { transform: scale(1.05); }
}

.test-status {
    margin-top: 15px;
    font-size: 1rem;
    color: #666;
    font-weight: 500;
}

/* Results Grid */
.results-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 20px;
    margin-bottom: 30px;
    opacity: 0;
    transform: translateY(20px);
    transition: all 0.5s ease;
}

.results-grid.show {
    opacity: 1;
    transform: translateY(0);
}

.result-card {
    background: linear-gradient(135deg, #f8f9fa, #e9ecef);
    border-radius: 15px;
    padding: 25px;
    text-align: center;
    transition: all 0.3s ease;
    border: 2px solid transparent;
}

.result-card:hover {
    transform: translateY(-5px);
    border-color: #667eea;
    box-shadow: 0 10px 30px rgba(102, 126, 234, 0.2);
}

.download-card:hover { border-color: #6bcf7f; }
.upload-card:hover { border-color: #ffd93d; }
.ping-card:hover { border-color: #ff6b6b; }

.result-icon {
    font-size: 2rem;
    margin-bottom: 15px;
    color: #667eea;
}

.download-card .result-icon { color: #6bcf7f; }
.upload-card .result-icon { color: #ffd93d; }
.ping-card .result-icon { color: #ff6b6b; }

.result-label {
    font-size: 0.9rem;
    color: #666;
    margin-bottom: 5px;
    font-weight: 500;
}

.result-value {
    font-size: 1.5rem;
    font-weight: 700;
    color: #333;
}

/* Progress Container */
.progress-container {
    display: none;
    text-align: center;
}

.progress-container.show {
    display: block;
    animation: fadeIn 0.3s ease;
}

@keyframes fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
}

.progress-bar {
    width: 100%;
    height: 8px;
    background: #e0e0e0;
    border-radius: 4px;
    overflow: hidden;
    margin-bottom: 15px;
}

.progress-fill {
    height: 100%;
    background: linear-gradient(90deg, #667eea, #764ba2);
    width: 0%;
    transition: width 0.3s ease;
    border-radius: 4px;
}

.progress-text {
    font-size: 1rem;
    color: #666;
    font-weight: 500;
}

/* Info Section */
.info-section {
    margin: 60px 0;
}

.info-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 30px;
}

.info-card {
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(10px);
    border-radius: 20px;
    padding: 30px;
    text-align: center;
    color: white;
    border: 1px solid rgba(255, 255, 255, 0.2);
    transition: all 0.3s ease;
}

.info-card:hover {
    transform: translateY(-10px);
    background: rgba(255, 255, 255, 0.15);
}

.info-card i {
    font-size: 3rem;
    margin-bottom: 20px;
    color: #ffd93d;
}

.info-card h3 {
    font-size: 1.3rem;
    margin-bottom: 15px;
    font-weight: 600;
}

.info-card p {
    opacity: 0.9;
    line-height: 1.6;
}

/* Footer */
.footer {
    text-align: center;
    padding: 30px 0;
    color: rgba(255, 255, 255, 0.8);
    border-top: 1px solid rgba(255, 255, 255, 0.1);
    margin-top: 60px;
}

/* Responsive Design */
@media (max-width: 768px) {
    .header {
        flex-direction: column;
        gap: 20px;
    }
    
    .hero-title {
        font-size: 2.5rem;
    }
    
    .widget-container {
        padding: 20px;
    }
    
    .gauge-svg {
        width: 250px;
        height: 150px;
    }
    
    .results-grid {
        grid-template-columns: 1fr;
    }
    
    .nav {
        gap: 20px;
    }
}
