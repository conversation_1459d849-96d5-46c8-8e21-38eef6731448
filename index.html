<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>SpeedTest Pro - Professional Internet Speed Testing</title>
    <link rel="stylesheet" href="styles.css">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
</head>
<body>
    <div class="container">
        <!-- Header -->
        <header class="header">
            <div class="logo">
                <i class="fas fa-tachometer-alt"></i>
                <span>SpeedTest Pro</span>
            </div>
            <nav class="nav">
                <a href="#" class="nav-link">Home</a>
                <a href="#" class="nav-link">History</a>
                <a href="#" class="nav-link">About</a>
            </nav>
        </header>

        <!-- Main Content -->
        <main class="main">
            <!-- Hero Section -->
            <section class="hero">
                <h1 class="hero-title">Test Your Internet Speed</h1>
                <p class="hero-subtitle">Get accurate measurements of your download, upload speeds and ping latency</p>
            </section>

            <!-- Speed Test Widget -->
            <section class="speed-test-widget">
                <div class="widget-container">
                    <!-- Speed Gauge -->
                    <div class="speed-gauge">
                        <div class="gauge-container">
                            <svg class="gauge-svg" viewBox="0 0 200 120">
                                <defs>
                                    <linearGradient id="gaugeGradient" x1="0%" y1="0%" x2="100%" y2="0%">
                                        <stop offset="0%" style="stop-color:#ff6b6b"/>
                                        <stop offset="50%" style="stop-color:#ffd93d"/>
                                        <stop offset="100%" style="stop-color:#6bcf7f"/>
                                    </linearGradient>
                                </defs>
                                <path class="gauge-bg" d="M 20 100 A 80 80 0 0 1 180 100" stroke="#e0e0e0" stroke-width="8" fill="none"/>
                                <path class="gauge-fill" d="M 20 100 A 80 80 0 0 1 180 100" stroke="url(#gaugeGradient)" stroke-width="8" fill="none" stroke-dasharray="251.2" stroke-dashoffset="251.2"/>
                                <circle class="gauge-center" cx="100" cy="100" r="4" fill="#333"/>
                                <line class="gauge-needle" x1="100" y1="100" x2="100" y2="30" stroke="#333" stroke-width="2" transform="rotate(-90 100 100)"/>
                            </svg>
                            <div class="speed-display">
                                <span class="speed-value">0</span>
                                <span class="speed-unit">Mbps</span>
                            </div>
                        </div>
                    </div>

                    <!-- Test Controls -->
                    <div class="test-controls">
                        <button class="start-test-btn" id="startTestBtn">
                            <i class="fas fa-play"></i>
                            <span>Start Test</span>
                        </button>
                        <div class="test-status" id="testStatus">Ready to test</div>
                    </div>

                    <!-- Results Display -->
                    <div class="results-grid" id="resultsGrid">
                        <div class="result-card download-card">
                            <div class="result-icon">
                                <i class="fas fa-download"></i>
                            </div>
                            <div class="result-info">
                                <div class="result-label">Download</div>
                                <div class="result-value" id="downloadSpeed">-- Mbps</div>
                            </div>
                        </div>
                        <div class="result-card upload-card">
                            <div class="result-icon">
                                <i class="fas fa-upload"></i>
                            </div>
                            <div class="result-info">
                                <div class="result-label">Upload</div>
                                <div class="result-value" id="uploadSpeed">-- Mbps</div>
                            </div>
                        </div>
                        <div class="result-card ping-card">
                            <div class="result-icon">
                                <i class="fas fa-clock"></i>
                            </div>
                            <div class="result-info">
                                <div class="result-label">Ping</div>
                                <div class="result-value" id="pingLatency">-- ms</div>
                            </div>
                        </div>
                    </div>

                    <!-- Progress Bar -->
                    <div class="progress-container" id="progressContainer">
                        <div class="progress-bar">
                            <div class="progress-fill" id="progressFill"></div>
                        </div>
                        <div class="progress-text" id="progressText">Preparing test...</div>
                    </div>
                </div>
            </section>

            <!-- Additional Info -->
            <section class="info-section">
                <div class="info-grid">
                    <div class="info-card">
                        <i class="fas fa-shield-alt"></i>
                        <h3>Secure Testing</h3>
                        <p>Your privacy is protected with encrypted connections</p>
                    </div>
                    <div class="info-card">
                        <i class="fas fa-chart-line"></i>
                        <h3>Accurate Results</h3>
                        <p>Multiple server locations for precise measurements</p>
                    </div>
                    <div class="info-card">
                        <i class="fas fa-mobile-alt"></i>
                        <h3>All Devices</h3>
                        <p>Works perfectly on desktop, tablet, and mobile</p>
                    </div>
                </div>
            </section>
        </main>

        <!-- Footer -->
        <footer class="footer">
            <p>&copy; 2024 SpeedTest Pro. All rights reserved.</p>
        </footer>
    </div>

    <!-- Particles Background -->
    <div class="particles" id="particles"></div>

    <script src="script.js"></script>
</body>
</html>
